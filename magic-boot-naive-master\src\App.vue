<template>
    <n-config-provider
        :locale="zhCN"
        :date-locale="dateZhCN"
        :theme="themeStore.naiveTheme"
        :theme-overrides="themeStore.naiveTheme"
    >
        <n-message-provider>
            <n-dialog-provider>
                <n-loading-bar-provider>
                    <router-view/>
                </n-loading-bar-provider>
            </n-dialog-provider>
        </n-message-provider>
    </n-config-provider>
</template>

<script setup>
import { NConfigProvider, zhCN, dateZhCN } from 'naive-ui'
import { useThemeStore } from '@/store/modules/themeStore'

const themeStore = useThemeStore()

</script>

<style lang="less">
@import 'styles/index.less';
.n-config-provider{
    height: 100%;
}
</style>
